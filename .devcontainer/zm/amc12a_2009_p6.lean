-- Proof content:
-- 1. [Problem Restatement] Determine which listed expression always equals \(12^{mn}\) when \(P = 2^{m}\) and \(Q = 3^{n}\). 2. [Key Idea] Rewrite each candidate in prime–factor form \(2^{(\cdot)}3^{(\cdot)}\) and match the exponents with those in \(12^{mn}=2^{2mn}3^{mn}\). 3. [Proof] Candidate (E): \[ P^{2n}Q^{m}=(2^{m})^{2n}(3^{n})^{m}=2^{2mn}\,3^{mn} =(2^{2}\!\cdot3)^{mn}=12^{mn}, \] so (E) matches exactly. For completeness, the remaining options give incorrect exponents on \(2\) or \(3\): (A) \(P^{2}Q=2^{2m}3^{n}\); (B) \(P^{n}Q^{m}=2^{mn}3^{mn}\); (C) \(P^{n}Q^{2m}=2^{mn}3^{2mn}\); (D) \(P^{2m}Q^{n}=2^{2m^{2}}3^{n^{2}}\). None of these equals \(2^{2mn}3^{mn}\) for all \(m,n\). 4. [Conclusion] Only \(P^{2n}Q^{m}\) (choice E) is identically equal to \(12^{mn}\).
