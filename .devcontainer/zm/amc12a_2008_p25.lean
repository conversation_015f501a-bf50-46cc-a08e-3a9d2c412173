-- Proof content:
-- 1. [Problem Restatement] Starting from the linear recurrence (a_{n+1}, b_{n+1}) = (√3 a_n − b_n, √3 b_n + a_n) and knowing (a_{100}, b_{100}) = (2, 4), find a₁ + b₁. 2. [Key Idea] Write the pair (a_n, b_n) as the single complex number z_n = a_n + i b_n; the given recurrence is then simple multiplication by √3 + i whose modulus is 2 and whose argument is 30°. 3. [Proof] Complex-number proof. (i) z_{n+1}=a_{n+1}+i b_{n+1} = (√3 a_n−b_n)+i(√3 b_n+a_n) = (a_n+i b_n)(√3+i) = (√3+i) z_n. Hence z_n = (√3+i)^{\,n−1} z_1. (ii) For n = 100, z_{100} = (√3+i)^{99} z_1 = 2+4i, so z_1 = (2+4i)/(√3+i)^{99}. (iii) Write √3+i = 2(cos 30° + i sin 30°); therefore (√3+i)^{99} = 2^{99}(cos (99·30°) + i sin (99·30°)) = 2^{99}(cos 90° + i sin 90°) = i·2^{99}. (iv) Hence z_1 = (2+4i)/(i·2^{99}) = (4 − 2i)/2^{99}. Thus a_1 = 4/2^{99}, b_1 = −2/2^{99}. (v) a_1 + b_1 = (4 − 2)/2^{99} = 2/2^{99} = 1/2^{98}. 4. [Conclusion] Therefore a₁ + b₁ = 1/2^{98}, i.e. option (D).
