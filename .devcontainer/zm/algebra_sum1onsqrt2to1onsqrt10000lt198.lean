import Mathlib.Data.Real.Sqrt
import Mathlib.Data.Finset.Sum
import Mathlib.Analysis.SpecialFunctions.Pow.Real
import Mathlib.Tactic.NormNum
import Mathlib.Tactic.Linarith

open Real Finset

-- Main theorem: Σ_{k=2}^{10000} 1/√k < 198
theorem sum_reciprocal_sqrt_bound :
  (∑ k in range 9999, 1 / sqrt (k + 2 : ℝ)) < 198 := by
  -- Use telescoping bound approach
  have h1 : ∀ k : ℕ, k ≥ 2 → 1 / sqrt k < 2 * (sqrt k - sqrt (k - 1)) := by
    -- This is the key telescoping inequality
    -- Can be proven by showing 1/√k < 2/(√k + √(k-1)) via √(k-1) < √k
    sorry

  have h2 : (∑ k in range 9999, 1 / sqrt (k + 2 : ℝ)) <
            (∑ k in range 9999, 2 * (sqrt (k + 2) - sqrt (k + 1))) := by
    -- This follows from applying h1 to each term in the sum
    sorry

  have h3 : (∑ k in range 9999, 2 * (sqrt (k + 2) - sqrt (k + 1))) =
            2 * (sqrt 10000 - sqrt 1) := by
    -- This follows from the telescoping sum property
    -- ∑_{k=0}^{9998} (√(k+2) - √(k+1)) = √10000 - √1
    sorry

  have h4 : 2 * (sqrt 10000 - sqrt 1) = 198 := by
    have h_10000 : sqrt 10000 = 100 := by
      rw [← sqrt_sq (by norm_num : 0 ≤ (100 : ℝ))]
      norm_num
    rw [h_10000, sqrt_one]
    norm_num

  -- Combine all steps
  calc (∑ k in range 9999, 1 / sqrt (k + 2 : ℝ))
    < (∑ k in range 9999, 2 * (sqrt (k + 2) - sqrt (k + 1))) := h2
    _ = 2 * (sqrt 10000 - sqrt 1) := h3
    _ = 198 := h4
