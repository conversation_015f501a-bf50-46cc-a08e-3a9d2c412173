-- Proof content:
-- 1. [Problem Restatement] Determine how many positive integers m admit at least one positive integer n such that mn ≤ m + n. 2. [Key Idea] Simply pick n = 1; then mn ≤ m + n becomes the trivial inequality m ≤ m + 1, true for every positive m. 3. [Proof] Let m be any positive integer and choose n = 1. Then mn = m·1 = m ≤ m + 1 = m + n. Because this holds for every positive m, each m satisfies the required condition. (Alternate viewpoint) Rewrite the inequality as (m – 1)(n – 1) ≤ 1. Setting n = 1 makes the left–hand side 0 ≤ 1, again valid for all positive m. 4. [Conclusion] Since every positive integer m works, there are infinitely many such m, i.e. choice (E).
