-- Proof content:
-- 1. [Problem Restatement] Given real numbers a, b with a² + b² = 1, prove ab + |a − b| ≤ 1. 2. [Key Idea] Rewrite |a − b| using the constraint a² + b² = 1; the inequality then becomes a single-variable problem in t = ab that is trivial to check. 3. [Proof] Algebraic proof. a) |a − b| = √((a − b)²) = √(a² + b² − 2ab) = √(1 − 2ab). b) Set t = ab (note t ≤ ½ because (a − b)² ≥ 0 ⇒ 1 − 2t ≥ 0). The desired inequality becomes t + √(1 − 2t) ≤ 1, (∗) for t ≤ ½. c) Both sides of (∗) are non-negative, so square once without changing the direction: 1 − 2t ≤ (1 − t)² = 1 − 2t + t². This simplifies to t² ≥ 0, which is always true. d) Therefore (∗) holds, and hence ab + |a − b| ≤ 1. Equality occurs when t² = 0 ⇔ ab = 0, i.e. (a, b) = (±1, 0) or (0, ±1). Alternative trigonometric proof. Write a = cos θ, b = sin θ (θ ∈ ℝ). Then ab + |a − b| = ½ sin 2θ + |cos θ − sin θ| = ½ sin 2θ + √(1 − sin 2θ). Let s = sin 2θ (so −1 ≤ s ≤ 1). The expression becomes ½s + √(1 − s) ≤ 1, which is verified exactly as in the algebraic proof by setting s = 2t and repeating step c). 4. [Conclusion] For any real a, b with a² + b² = 1, the maximum of ab + |a − b| is 1, attained precisely when one of a, b equals 0.
