import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.FieldSimp
import Mathlib.Analysis.SpecialFunctions.Pow.Real

theorem algebra_cubrtrp1oncubrtreq3_rcubp1onrcubeq5778 (r : ℝ) (hr_pos : 0 < r) :
  r^(1/3 : ℝ) + 1/(r^(1/3 : ℝ)) = 3 → r^3 + 1/r^3 = 5778 := by
  intro h
  -- SUBGOAL_001_ALT: Assume r > 0 and work directly with algebraic identities
  set x := r^(1/3 : ℝ) with hx_def
  have h_rewritten : x + 1/x = 3 := by
    rw [hx_def]
    exact h
  have hx_pos : 0 < x := by
    rw [hx_def]
    exact Real.rpow_pos_of_pos hr_pos _
  have hx_ne_zero : x ≠ 0 := ne_of_gt hx_pos
  have hr_cube : r = x^3 := by
    rw [hx_def]
    rw [← Real.rpow_natCast]
    rw [← Real.rpow_mul (le_of_lt hr_pos)]
    norm_num
  -- SUBGOAL_002: From x + 1/x = 3, derive x³ + 1/x³ = 18
  have h_cube_expansion : x^3 + 1/x^3 = (x + 1/x)^3 - 3*(x + 1/x) := by
    field_simp [hx_ne_zero]
    ring
  have h_x_cube : x^3 + 1/x^3 = 18 := by
    rw [h_cube_expansion, h_rewritten]
    norm_num
  -- SUBGOAL_003: From x³ + 1/x³ = 18, derive x⁹ + 1/x⁹ = 5778
  have h_ninth_expansion : x^9 + 1/x^9 = (x^3 + 1/x^3)^3 - 3*(x^3 + 1/x^3) := by
    have hx3_ne_zero : x^3 ≠ 0 := pow_ne_zero 3 hx_ne_zero
    field_simp [hx3_ne_zero]
    ring
  have h_x_ninth : x^9 + 1/x^9 = 5778 := by
    rw [h_ninth_expansion, h_x_cube]
    norm_num
  -- SUBGOAL_004: Translate x⁹ + 1/x⁹ = 5778 back to r³ + 1/r³ = 5778
  have hr_ninth : r^3 = x^9 := by
    rw [hr_cube]
    ring
  rw [hr_ninth]
  exact h_x_ninth
